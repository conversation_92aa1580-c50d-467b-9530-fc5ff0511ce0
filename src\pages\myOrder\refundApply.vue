<route lang="json5">
{
  style: {
    navigationBarTitleText: '申请售后',
  },
}
</route>
<script setup lang="ts">
import { creteRefundOrderApi, CreteRefundOrderParams } from '@/service/orderApi'
import { UploadImageRes, uploadRefundImagePath } from '@/service/agencyServiceApi'
import { useUserStore } from '@/store/user'
import { BASE64IMG_PREFIX, UPLOAD_IMG_MAXSIZE } from '@/enums'
import { useRefundStore } from '@/store/refundStore'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const refundStore = useRefundStore()
const { refundGoodsInfo } = storeToRefs(refundStore)
const BASEURL = import.meta.env.VITE_SERVER_BASEURL

// 页面状态
const loading = ref(false)
const form = ref(null)
const refundImages = ref([])

// 表单数据
const model = reactive<CreteRefundOrderParams>({
  orderCode: '',
  refundReason: '',
  images: '',
})

// 表单验证规则
const rules = {
  refundReason: [
    { required: true, message: '请输入退款原因', trigger: ['blur'] },
    { min: 5, max: 200, message: '退款原因长度应在5-200字符之间', trigger: ['blur'] },
  ],
}

// 获取页面参数
onLoad((options) => {
  if (options.orderCode) {
    model.orderCode = options.orderCode

    // 如果refundGoodsInfo中没有商品信息，可能是直接通过url进入页面
    // 此时可以尝试通过API获取订单信息（这里省略，实际项目中可以添加）
    if (!refundGoodsInfo.value.goodsName && !refundGoodsInfo.value.goodsImage) {
      uni.showToast({
        title: '正在获取订单信息',
        icon: 'loading',
        duration: 1000,
      })
    }
  } else {
    uni.showToast({
      title: '订单号不能为空',
      icon: 'none',
      duration: 2000,
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  }
})

// 页面卸载时清空商品信息
onUnload(() => {
  refundStore.clearRefundGoodsInfo()
})

// 删除图片
const deleteRefundImage = (event) => {
  refundImages.value.splice(event.index, 1)
  updateImagesString()
}

// 图片上传后处理
const refundImageAfterRead = (event) => {
  handleFileUpload(event, refundImages)
}

// 处理文件上传
const handleFileUpload = async (event, filesRef) => {
  const lists = [].concat(event.file)
  let fileListLen = filesRef.value.length

  // 添加上传状态
  lists.forEach((item) => {
    filesRef.value.push({ ...item, status: 'uploading', message: '上传中' })
  })

  // 逐个上传并更新状态
  for (let i = 0; i < lists.length; i++) {
    try {
      const result = await uploadFilePromise(lists[i].url)
      const item = filesRef.value[fileListLen]

      // 更新文件状态
      filesRef.value.splice(fileListLen, 1, {
        ...item,
        status: 'success',
        message: '',
        url: BASE64IMG_PREFIX + result.image,
        realUrl: result.url,
      })
      fileListLen++
    } catch (error) {
      // 上传失败处理
      const item = filesRef.value[fileListLen]
      filesRef.value.splice(fileListLen, 1, {
        ...item,
        status: 'failed',
        message: '上传失败',
      })
      fileListLen++

      uni.showToast({
        title: '图片上传失败',
        icon: 'none',
        duration: 2000,
      })
    }
  }

  // 更新图片字符串
  updateImagesString()
}

// 上传文件Promise
const uploadFilePromise = (url) => {
  return new Promise<UploadImageRes['data']>((resolve, reject) => {
    uni.uploadFile({
      url: BASEURL + uploadRefundImagePath,
      filePath: url,
      name: 'file',
      header: {
        Authorization: userStore.authorization,
      },
      success: (res) => {
        console.log('上传成功:', res)
        const d = JSON.parse(res.data) as UploadImageRes
        if (d.success) {
          resolve(d.data)
        } else {
          reject(d.errMessage)
        }
      },
      fail: (err) => {
        console.log('上传失败:', err)
        reject(err)
      },
    })
  })
}

// 更新图片字符串
const updateImagesString = () => {
  const imageUrls = refundImages.value
    .filter((item) => item.status === 'success' && item.realUrl)
    .map((item) => item.realUrl)
  model.images = imageUrls.join(',')
}

// 提交售后申请
const handleSubmit = () => {
  if (loading.value) return

  // 检查订单号是否存在
  if (!model.orderCode) {
    uni.showToast({
      title: '订单号不能为空',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  form.value
    .validate()
    .then((valid) => {
      if (valid) {
        loading.value = true

        creteRefundOrderApi({
          orderCode: model.orderCode.trim(),
          refundReason: model.refundReason.trim(),
          images: model.images,
        })
          .then(() => {
            uni.showToast({
              title: '售后申请提交成功',
              icon: 'success',
              duration: 2000,
            })

            setTimeout(() => {
              uni.navigateBack()
            }, 2000)
          })
          .catch((error) => {
            console.error('提交失败:', error)
            uni.showToast({
              title: error.errMessage || '提交失败，请重试',
              icon: 'none',
              duration: 2000,
            })
          })
          .finally(() => {
            loading.value = false
          })
      }
    })
    .catch((err) => {
      console.log('表单验证失败:', err)
      uni.pageScrollTo({
        selector: '#formTop',
      })
      const title = err[0]?.message ?? '请填写必填项'
      uni.showToast({
        title,
        icon: 'none',
      })
    })
}
</script>

<template>
  <view class="p-3 space-y-2">
    <!-- 页面标题说明 -->
    <view class="bg-white rounded-sm p-4">
      <view class="text-lg font-bold mb-2">申请售后</view>
      <view class="text-sm text-gray-600">请填写以下信息提交售后申请，我们会尽快为您处理</view>
    </view>

    <!-- 商品信息展示 -->
    <view class="bg-white rounded-sm p-4">
      <view class="text-xs text-gray-500">成交时间：{{ refundGoodsInfo.payDate }}</view>
      <view class="text-xs text-gray-500">订单编号：{{ refundGoodsInfo.orderCode }}</view>
      <view class="flex space-x-3 mt-3">
        <image
          v-if="refundGoodsInfo.goodsImage"
          class="w-20 h-20 rounded-sm"
          mode="aspectFill"
          :src="refundGoodsInfo.goodsImage"
        />
        <view v-else class="w-20 h-20 bg-gray-200 rounded-sm"></view>
        <view class="flex-1">
          <view class="font-medium mb-1">
            {{ refundGoodsInfo.goodsName }}
          </view>
          <view class="text-sm text-gray-500 mb-1">
            {{ refundGoodsInfo.orderContent }}
          </view>
        </view>
      </view>
    </view>

    <!-- 表单区域 -->
    <up-form labelWidth="80" ref="form" labelPosition="top" :model="model" :rules="rules">
      <view id="formTop" class="bg-white rounded-sm py-4 pl-6 pr-4">
        <!-- 退款原因 -->
        <up-form-item label="退款原因" prop="refundReason" required>
          <up-textarea
            v-model="model.refundReason"
            placeholder="请详细描述退款原因（5-200字符）"
            :maxlength="200"
            :showWordLimit="true"
            :autoHeight="true"
            border="bottom"
          />
        </up-form-item>
      </view>
    </up-form>

    <!-- 图片上传区域 -->
    <view class="bg-white rounded-sm p-4">
      <view class="mb-3">
        <text class="text-base font-medium">上传相关图片</text>
        <text class="text-sm text-gray-500 ml-2">（可选，最多上传9张）</text>
      </view>

      <up-upload
        :fileList="refundImages"
        @afterRead="refundImageAfterRead"
        @delete="deleteRefundImage"
        name="refundImages"
        :multiple="true"
        :maxCount="9"
        :maxSize="UPLOAD_IMG_MAXSIZE"
        :previewFullImage="true"
        accept="image"
      >
        <view class="upload-slot">
          <up-icon name="camera" size="40" color="#c0c4cc"></up-icon>
          <text class="text-sm text-gray-500 mt-2">上传图片</text>
        </view>
      </up-upload>

      <view class="text-xs text-gray-500 mt-2">支持jpg、png格式，单张图片不超过3MB</view>
    </view>

    <!-- 温馨提示 -->
    <view class="bg-orange-50 rounded-sm p-4">
      <view class="flex items-start">
        <up-icon name="info-circle" color="#ff9500" size="16" class="mt-1 mr-2"></up-icon>
        <view class="flex-1">
          <view class="text-sm font-medium text-orange-600 mb-1">温馨提示</view>
          <view class="text-xs text-orange-600 leading-relaxed">
            1. 详细描述退款原因有助于我们更快处理
            <br />
            2. 如有相关凭证图片，建议一并上传
            <br />
            3. 售后申请提交后，我们会在1-3个工作日内处理
            <br />
            4. 如有疑问，请联系客服咨询
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="mt-2">
      <view
        class="p-3 flex items-center justify-center color-white font-bold rounded-lg"
        :class="loading ? 'o-bg-primary-disable' : 'o-bg-primary'"
        @click="handleSubmit"
      >
        <up-loading-icon
          v-if="loading"
          mode="circle"
          color="white"
          size="20"
          class="mr-2"
        ></up-loading-icon>
        {{ loading ? '提交中...' : '提交售后申请' }}
      </view>
    </view>

    <!-- 底部间距 -->
    <view class="p-8"></view>
  </view>
</template>

<style scoped lang="scss">
.upload-slot {
  width: 80px;
  height: 80px;
  border: 1px dashed #c0c4cc;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.upload-slot:active {
  background-color: #f0f0f0;
}
</style>
