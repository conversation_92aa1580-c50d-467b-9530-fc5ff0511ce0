<route lang="json5">
{
  style: {
    navigationBarTitleText: '支付成功',
    backgroundColor: '#f0f3f8',
  },
}
</route>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const orderCode = ref('')
const totalPrice = ref(0)
// 支付来源，用于区分不同类型的订单
const source = ref('')
// 返回页面路径
const backPage = ref('/pages/index/index')
// 订单页面路径
const orderPage = ref('/pages/myOrder/index')

onLoad((option) => {
  // 获取订单编号和金额，以及来源
  if (option.orderCode) {
    orderCode.value = option.orderCode
  }
  if (option.totalPrice) {
    totalPrice.value = Number(option.totalPrice)
  }
  if (option.source) {
    source.value = option.source
    // 根据来源设置不同的返回页和订单页
    switch (source.value) {
      case 'shoppingMall':
        backPage.value = '/pages/shoppingMall/index'
        break
      default:
        break
    }
  }
})

// 返回首页或指定页面
const goBack = () => {
  if (backPage.value.includes('pages/index/')) {
    uni.switchTab({
      url: backPage.value,
    })
  } else {
    uni.redirectTo({
      url: backPage.value,
    })
  }
}

// 查看订单
const goToOrder = () => {
  uni.navigateTo({
    url: orderPage.value,
  })
}
</script>

<template>
  <view class="payment-success">
    <!-- 成功图标 -->
    <view class="success-icon">
      <up-icon name="checkmark-circle" color="#07c160" :size="80"></up-icon>
    </view>

    <!-- 成功信息 -->
    <view class="success-info">
      <view class="success-title">支付成功</view>
      <view class="success-price" v-if="totalPrice">¥{{ totalPrice.toFixed(2) }}</view>
      <view class="success-order" v-if="orderCode">订单编号：{{ orderCode }}</view>
    </view>

    <!-- 分割线 -->
    <view class="divider"></view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="btn btn-outline" @click="goBack">返回首页</view>
      <view class="btn btn-primary" @click="goToOrder">查看订单</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.payment-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  background-color: #ffffff;
  min-height: 100vh;
}

.success-icon {
  margin-top: 60px;
  margin-bottom: 30px;
}

.success-info {
  width: 100%;
  text-align: center;
  margin-bottom: 40px;
}

.success-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.success-price {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.success-order {
  font-size: 14px;
  color: #999;
}

.divider {
  width: 100%;
  height: 1px;
  background-color: #f0f0f0;
  margin: 20px 0;
}

.action-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 0 20px;
  margin-top: 30px;
}

.btn {
  width: 45%;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.btn-outline {
  border: 1px solid #07c160;
  color: #07c160;
}

.btn-primary {
  background-color: #07c160;
  color: #ffffff;
}
</style>
